[project]
name = "ai-toolbox-backend"
version = "0.1.0"
description = "AI Toolbox Backend API Service"
readme = "README.md"
requires-python = ">=3.11"
authors = [
    { name = "AI Toolbox Team" }
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    # Web Framework
    "fastapi[standard]>=0.113.0",
    "uvicorn[standard]>=0.24.0",
    
    # Data Validation & Serialization
    "pydantic>=2.8.0",
    "pydantic-settings>=2.1.0",
    
    # Authentication & Security
    "python-jose[cryptography]>=3.3.0",
    "python-multipart>=0.0.9",
    "passlib[bcrypt]>=1.7.4",
    
    # AI & LangChain
    "langchain>=0.2.0",
    "langchain-openai>=0.1.0",
    "langchain-community>=0.2.0",
    
    # Azure Integration
    "azure-identity>=1.15.0",
    "openai>=1.3.0",
    
    # Database & Storage
    "sqlalchemy>=2.0.0",
    "asyncpg>=0.29.0",
    "redis>=5.0.0",
    
    # HTTP Client
    "httpx>=0.27.0",
    
    # Utilities
    "python-dotenv>=1.0.0",
]

[dependency-groups]
dev = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
    "black>=24.0.0",
    "isort>=5.13.0",
    "mypy>=1.8.0",
    "pre-commit>=3.6.0",
]

test = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=4.0.0",
    "httpx>=0.27.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
ignore_missing_imports = true
warn_unused_ignores = true
warn_redundant_casts = true
warn_unused_configs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto" 