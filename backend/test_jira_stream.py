#!/usr/bin/env python3
"""Test Jira streaming functionality with fixed authentication."""

import asyncio
import sys
from app.services.azure_ai import azure_ai_service

async def test_jira_streaming():
    """Test Jira comment streaming with Bearer token authentication."""
    print("=== Jira Streaming Test ===")
    
    try:
        print("1. Testing Jira comment streaming...")
        
        # Test streaming generation
        print("\n📝 Streaming response:")
        print("-" * 50)
        
        full_content = ""
        async for chunk in azure_ai_service.generate_jira_comment_stream(
            task_description="实现用户登录功能",
            task_type="feature",
            context={"priority": "high", "team": "frontend"}
        ):
            print(chunk, end="", flush=True)
            full_content += chunk
        
        print("\n" + "-" * 50)
        print(f"✅ Streaming completed. Total length: {len(full_content)} characters")
        
        # Test non-streaming generation
        print("\n2. Testing non-streaming generation...")
        result = await azure_ai_service.generate_jira_comment(
            task_description="修复登录页面的样式问题",
            task_type="bug",
            context={"severity": "medium"}
        )
        
        if result.get("generated_content"):
            print(f"✅ Non-streaming completed. Content length: {len(result['generated_content'])} characters")
            print(f"Model used: {result.get('model', 'unknown')}")
            return True
        else:
            print(f"❌ Non-streaming failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_jira_streaming())
    print(f"\nJira streaming test: {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
