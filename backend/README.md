# AI Toolbox Backend

AI Toolbox backend service providing secure AI functionality support for browser extensions.

## 🚀 New Features

- 🔐 **OAuth Token Authentication**: Support for Azure OAuth token authentication with enhanced security
- 🤖 **LangChain Integration**: Intelligent conversations and specialized AI agents using LangChain
- 💬 **Universal Streaming Support**: All AI endpoints now support both streaming and non-streaming responses via `stream` parameter
- 🎯 **Specialized Scenarios**: Optimized content generation for Jira and GitHub workflows
- ⚡ **High Performance**: Asynchronous API service based on FastAPI
- 🛡️ **Type Safety**: Complete Pydantic data validation
- 🔄 **Smart Token Management**: Automatic token refresh and retry mechanisms

## Authentication Methods

### Recommended: OAuth Token Authentication (More Secure)

Refer to the implementation in `lib/ai/azure-auth.ts`, using Azure OAuth client credentials flow:

```bash
# Configure in .env
MS_OAUTH_CLIENT_ID=your-azure-app-client-id
MS_OAUTH_CLIENT_SECRET=your-azure-app-client-secret
MS_OAUTH_GRANT_TYPE=client_credentials
MS_OAUTH_SCOPE=https://cognitiveservices.azure.com/.default
MS_OAUTH_URL=https://login.microsoftonline.com/your-tenant-id/oauth2/v2.0/token
```

### Alternative: API Key Authentication

```bash
# Configure in .env
AZURE_OPENAI_API_KEY=your-azure-openai-api-key
```

## Technology Stack

- **Framework**: FastAPI + Uvicorn
- **AI Services**: Azure OpenAI + LangChain
- **Authentication**: OAuth 2.0 Client Credentials Flow
- **Data Validation**: Pydantic v2
- **Package Management**: uv
- **Python**: 3.11+

## Quick Start

### Requirements

- Python 3.11 or higher
- uv package manager

### Install Dependencies

```bash
# Install project dependencies
uv sync

# Install development dependencies
uv sync --dev
```

### Environment Configuration

1. Copy the environment variables example file:
```bash
cp env.example .env
```

2. Edit the `.env` file and configure OAuth authentication:
```bash
# Recommended OAuth authentication
MS_OAUTH_CLIENT_ID=your-azure-app-client-id
MS_OAUTH_CLIENT_SECRET=your-azure-app-client-secret
MS_OAUTH_URL=https://login.microsoftonline.com/your-tenant-id/oauth2/v2.0/token

# Azure OpenAI endpoint
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4o

# Application secrets
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key
```

### Run the Service

```bash
# Development environment
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Or use the development script
uv run python run_dev.py
```

After starting the service, access:

- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **API Root**: http://localhost:8000/api/v1

## 🔄 API Endpoints

### 🆕 Universal Streaming Support

All AI endpoints now support both streaming and non-streaming responses through the `stream` parameter:

- **Non-streaming** (default): `"stream": false` - Returns complete response at once
- **Streaming**: `"stream": true` - Returns real-time streaming response via Server-Sent Events

### AI Services

- `POST /api/v1/ai/generate` - 🆕 **Universal text generation** (supports `stream` parameter)
- `POST /api/v1/ai/chat/simple` - 🆕 **Simple chat conversation** (supports `stream` parameter)
- `POST /api/v1/ai/chat/stream` - 🆕 **Chat conversation** (supports `stream` parameter)
- `POST /api/v1/ai/jira/generate` - 🆕 **Jira task content generation** (supports `stream` parameter)
- `POST /api/v1/ai/github/pr` - 🆕 **GitHub PR description generation** (supports `stream` parameter)
- `POST /api/v1/ai/auth/refresh` - Refresh authentication tokens
- `GET /api/v1/ai/health` - AI service health check

### Authentication Services

- `POST /api/v1/auth/login` - User login
- `GET /api/v1/auth/me` - Get user information
- `POST /api/v1/auth/logout` - User logout
- `GET /api/v1/auth/health` - Authentication service health check

### 🆕 Universal Streaming Usage Examples

#### Text Generation with Stream Control

```javascript
// Non-streaming response (traditional)
const nonStreamResponse = await fetch('/api/v1/ai/generate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    prompt: 'Explain quantum computing',
    stream: false,  // Default behavior
    temperature: 0.7,
    max_tokens: 1000
  })
});

const result = await nonStreamResponse.json();
console.log('Complete response:', result.text);

// Streaming response (real-time)
const streamResponse = await fetch('/api/v1/ai/generate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    prompt: 'Explain quantum computing',
    stream: true,  // Enable streaming
    temperature: 0.7,
    max_tokens: 1000
  })
});

const reader = streamResponse.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const chunk = decoder.decode(value);
  const lines = chunk.split('\n');
  
  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = line.slice(6);
      if (data === '[DONE]') return;
      console.log('Streaming chunk:', data);
    }
  }
}
```

#### Jira Task Generation with Stream Control

```javascript
// Non-streaming Jira comment generation
async function generateJiraComment(taskData, useStream = false) {
  const response = await fetch('/api/v1/ai/jira/generate', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      task_description: taskData.description,
      task_type: taskData.type,
      context: taskData.context,
      stream: useStream  // Control streaming behavior
    })
  });
  
  if (useStream) {
    // Handle streaming response
    return handleStreamingResponse(response);
  } else {
    // Handle complete response
    const result = await response.json();
    return result.generated_content;
  }
}

// Usage examples
const completeResponse = await generateJiraComment({
  description: "Implement user authentication",
  type: "feature",
  context: { priority: "high" }
}, false);

const streamingResponse = await generateJiraComment({
  description: "Fix login bug",
  type: "bug",
  context: { severity: "critical" }
}, true);
```

#### GitHub PR Description with Stream Control

```javascript
// GitHub PR generation with flexible streaming
async function generatePRDescription(prData, options = {}) {
  const { useStream = false, realTimeDisplay = false } = options;
  
  const response = await fetch('/api/v1/ai/github/pr', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      pr_title: prData.title,
      code_changes: prData.changes,
      branch_name: prData.branch,
      commit_messages: prData.commits,
      stream: useStream
    })
  });
  
  if (useStream && realTimeDisplay) {
    // Real-time display for better UX
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullContent = '';
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') return fullContent;
          
          fullContent += data;
          // Update UI in real-time
          updatePRDescriptionPreview(fullContent);
        }
      }
    }
    
    return fullContent;
  } else {
    // Get complete response
    const result = await response.json();
    return result.generated_description;
  }
}
```

#### Chat with Stream Control

```javascript
// Flexible chat implementation
class AIChat {
  constructor(streamingEnabled = true) {
    this.streamingEnabled = streamingEnabled;
  }
  
  async sendMessage(message, options = {}) {
    const { 
      useStream = this.streamingEnabled,
      onChunk = null,
      onComplete = null 
    } = options;
    
    const response = await fetch('/api/v1/ai/chat/simple', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message,
        stream: useStream,
        temperature: 0.7,
        max_tokens: 1000
      })
    });
    
    if (useStream) {
      return this.handleStreamingChat(response, onChunk, onComplete);
    } else {
      const result = await response.json();
      if (onComplete) onComplete(result.text);
      return result.text;
    }
  }
  
  async handleStreamingChat(response, onChunk, onComplete) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullResponse = '';
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') {
            if (onComplete) onComplete(fullResponse);
            return fullResponse;
          }
          
          fullResponse += data;
          if (onChunk) onChunk(data, fullResponse);
        }
      }
    }
    
    return fullResponse;
  }
}

// Usage examples
const chat = new AIChat(true); // Enable streaming by default

// Streaming chat with real-time updates
await chat.sendMessage('Help me write a commit message', {
  useStream: true,
  onChunk: (chunk, fullText) => {
    console.log('New chunk:', chunk);
    updateChatUI(fullText);
  },
  onComplete: (finalText) => {
    console.log('Chat complete:', finalText);
    finalizeChatUI(finalText);
  }
});

// Non-streaming chat for simple use cases
const quickResponse = await chat.sendMessage('What is React?', {
  useStream: false
});
console.log('Quick response:', quickResponse);
```

## Development Guide

### Project Structure

```
backend/
├── app/                    # Main application directory
│   ├── api/               # API routes (ai.py, chat.py, auth.py)
│   ├── services/          # Business logic & AI services
│   │   ├── base_ai.py    # Smart token management
│   │   ├── chat_service.py # Chat service
│   │   └── azure_oauth.py # OAuth token management
│   ├── prompts/          # Specialized prompt templates
│   ├── models/           # Pydantic data models
│   ├── utils/            # Utility functions
│   │   └── stream_handler.py # 🆕 Universal streaming utilities
│   └── config.py         # Configuration management
├── tests/                # Test files
├── wiki/                 # Documentation & troubleshooting
└── pyproject.toml        # Project configuration
```

### Code Quality

Run code formatting:
```bash
uv run black app/
uv run isort app/
```

Run type checking:
```bash
uv run mypy app/
```

Run tests:
```bash
uv run pytest
```

## Frontend Integration

### Migration from Direct Azure Calls

The previous `lib/ai/azure-auth.ts` logic has been moved to the backend for better security and token management. Frontend can now call backend APIs directly:

```typescript
// ❌ Before: Direct Azure OpenAI calls (insecure)
const token = await getToken();
const response = await azureOpenAI.chat.completions.create({...});

// ✅ After: Secure backend API calls with stream control
const response = await fetch('http://localhost:8000/api/v1/ai/generate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ 
    prompt: generatePrompt(description),
    model: 'gpt-4o',
    max_tokens: 1000,
    stream: false, // or true for streaming
    system_message: 'You are a professional development assistant...'
  })
});

if (request.stream) {
  // Handle streaming response
  const reader = response.body.getReader();
  // ... streaming logic
} else {
  // Handle complete response
  const result = await response.json();
  console.log('Generated text:', result.text);
}
```

### Universal Streaming Client

```typescript
// Universal AI client with streaming support
class AIToolboxClient {
  private baseUrl = 'http://localhost:8000/api/v1';
  
  async generateText(prompt: string, options?: {
    model?: string;
    max_tokens?: number;
    temperature?: number;
    system_message?: string;
    stream?: boolean;
    onChunk?: (chunk: string) => void;
  }) {
    const { stream = false, onChunk, ...requestOptions } = options || {};
    
    const response = await fetch(`${this.baseUrl}/ai/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        prompt,
        stream,
        ...requestOptions
      })
    });
    
    if (stream) {
      return this.handleStreamingResponse(response, onChunk);
    } else {
      const result = await response.json();
      return result.text;
    }
  }
  
  async generateJiraComment(taskData: {
    task_description: string;
    task_type?: string;
    context?: Record<string, any>;
    stream?: boolean;
    onChunk?: (chunk: string) => void;
  }) {
    const { stream = false, onChunk, ...requestData } = taskData;
    
    const response = await fetch(`${this.baseUrl}/ai/jira/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...requestData,
        stream
      })
    });
    
    if (stream) {
      return this.handleStreamingResponse(response, onChunk);
    } else {
      const result = await response.json();
      return result.generated_content;
    }
  }
  
  async generateGitHubPR(prData: {
    pr_title: string;
    code_changes: string;
    branch_name?: string;
    commit_messages?: string[];
    stream?: boolean;
    onChunk?: (chunk: string) => void;
  }) {
    const { stream = false, onChunk, ...requestData } = prData;
    
    const response = await fetch(`${this.baseUrl}/ai/github/pr`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...requestData,
        stream
      })
    });
    
    if (stream) {
      return this.handleStreamingResponse(response, onChunk);
    } else {
      const result = await response.json();
      return result.generated_description;
    }
  }
  
  private async handleStreamingResponse(
    response: Response, 
    onChunk?: (chunk: string) => void
  ): Promise<string> {
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();
    let fullContent = '';
    
    while (true) {
      const { done, value } = await reader!.read();
      if (done) break;
      
      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') return fullContent;
          
          fullContent += data;
          if (onChunk) onChunk(data);
        }
      }
    }
    
    return fullContent;
  }
}

// Usage examples
const aiClient = new AIToolboxClient();

// Non-streaming usage
const result = await aiClient.generateText('Help me write a commit message');

// Streaming usage with real-time updates
const streamResult = await aiClient.generateText('Explain React hooks', {
  stream: true,
  onChunk: (chunk) => {
    console.log('Received chunk:', chunk);
    updateUI(chunk);
  }
});
```

### Error Handling Best Practices

```typescript
// Robust error handling for both streaming and non-streaming
async function callAIServiceWithRetry(
  endpoint: string, 
  data: any, 
  options: { 
    maxRetries?: number;
    stream?: boolean;
    onChunk?: (chunk: string) => void;
  } = {}
) {
  const { maxRetries = 3, stream = false, onChunk } = options;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(`/api/v1/ai/${endpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...data, stream })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      if (stream) {
        return await handleStreamingResponse(response, onChunk);
      } else {
        const result = await response.json();
        if (result.success !== false) {
          return result;
        } else {
          throw new Error(result.error || 'Unknown error');
        }
      }
      
    } catch (error) {
      console.warn(`Attempt ${attempt} failed:`, error);
      
      if (attempt === maxRetries) {
        throw new Error(`Failed after ${maxRetries} attempts: ${error}`);
      }
      
      // Wait before retry (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
}
```

## Advantages Comparison

### OAuth Token vs API Key

| Feature | OAuth Token | API Key |
|---------|-------------|---------|
| **Security** | ✅ Auto-expiring, revocable | ⚠️ Long-lived |
| **Access Control** | ✅ Fine-grained permissions | ❌ Full permissions |
| **Audit Trail** | ✅ Complete logging | ⚠️ Limited |
| **Enterprise Compliance** | ✅ Enterprise-grade security | ❌ Not recommended |

### Universal Streaming vs Traditional Approaches

| Feature | Universal Streaming | Traditional Separate Endpoints |
|---------|-------------------|-------------------------------|
| **API Consistency** | ✅ Single endpoint per function | ❌ Multiple endpoints |
| **Development Complexity** | ✅ Simple parameter control | ⚠️ Different implementations |
| **User Experience** | ✅ Flexible UX options | ⚠️ Fixed UX patterns |
| **Maintenance** | ✅ Single codebase | ❌ Duplicate logic |

### LangChain Enhancements

- 🧠 **Smart Prompts**: Specialized prompt templates for different scenarios
- 🔧 **Tool Chain**: Support for extending more AI tools and agents
- 📊 **Structured Output**: Better data parsing and processing
- 🔄 **Workflows**: Support for complex AI workflow orchestration

## 🔧 OAuth Token Management Features

### Smart Token Refresh
- **Proactive Refresh**: Tokens are refreshed 5 minutes before expiration
- **Automatic Retry**: Authentication errors trigger automatic token refresh and retry
- **Error Classification**: Distinguishes authentication errors from other failures
- **Client Management**: Clients are recreated when tokens are refreshed

### Troubleshooting
For detailed OAuth token troubleshooting, see: [wiki/oauth-token-troubleshooting.md](wiki/oauth-token-troubleshooting.md)

## Deployment

### Docker Deployment

```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY pyproject.toml .
RUN pip install uv
RUN uv pip install -r pyproject.toml

COPY app/ ./app/

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Environment Variables

Key environment variables for production:

- `MS_OAUTH_CLIENT_ID`: Azure application client ID
- `MS_OAUTH_CLIENT_SECRET`: Azure application client secret  
- `MS_OAUTH_URL`: OAuth token endpoint
- `AZURE_OPENAI_ENDPOINT`: Azure OpenAI service endpoint
- `SECRET_KEY`: Application secret key
- `JWT_SECRET_KEY`: JWT signing key
- `ENVIRONMENT`: Environment identifier (production)

## Contributing

1. Fork the project
2. Create a feature branch
3. Commit your changes
4. Create a Pull Request

## License

MIT License

## Support

For issues, please submit an Issue or contact the development team. 