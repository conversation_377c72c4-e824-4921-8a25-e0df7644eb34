#!/usr/bin/env python3
"""Test Jira API streaming endpoint."""

import asyncio
import aiohttp
import json

async def test_jira_api_streaming():
    """Test the Jira API streaming endpoint."""
    print("=== Testing Jira API Streaming Endpoint ===")
    
    url = "http://localhost:8000/api/v1/ai/jira/generate"
    payload = {
        "task_description": "实现用户注册功能，包括邮箱验证",
        "task_type": "feature",
        "context": {
            "priority": "high",
            "team": "frontend",
            "estimated_hours": 16
        },
        "stream": True
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            print(f"📡 Sending request to: {url}")
            print(f"📝 Payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")
            
            async with session.post(url, json=payload) as response:
                print(f"📊 Response status: {response.status}")
                print(f"📋 Response headers: {dict(response.headers)}")
                
                if response.status == 200:
                    print("\n🔄 Streaming response:")
                    print("-" * 60)
                    
                    full_content = ""
                    async for line in response.content:
                        line_str = line.decode('utf-8')
                        if line_str.startswith('data: '):
                            data = line_str[6:].strip()
                            if data == '[DONE]':
                                print("\n" + "-" * 60)
                                print("✅ Streaming completed!")
                                break
                            elif data.startswith('Error:'):
                                print(f"\n❌ Error received: {data}")
                                break
                            else:
                                print(data, end="", flush=True)
                                full_content += data
                    
                    print(f"\n📊 Total content length: {len(full_content)} characters")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ API call failed: {response.status}")
                    print(f"Error: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

async def test_jira_api_non_streaming():
    """Test the Jira API non-streaming endpoint."""
    print("\n=== Testing Jira API Non-Streaming Endpoint ===")
    
    url = "http://localhost:8000/api/v1/ai/jira/generate"
    payload = {
        "task_description": "修复登录页面的响应式布局问题",
        "task_type": "bug",
        "context": {
            "severity": "medium",
            "browser": "Chrome, Firefox",
            "affected_users": "mobile users"
        },
        "stream": False
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            print(f"📡 Sending request to: {url}")
            
            async with session.post(url, json=payload) as response:
                print(f"📊 Response status: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    print("✅ Non-streaming response received!")
                    print(f"📊 Content length: {len(result.get('generated_content', ''))} characters")
                    print(f"🤖 Model used: {result.get('model', 'unknown')}")
                    print(f"🔢 Tokens used: {result.get('tokens_used', 0)}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ API call failed: {response.status}")
                    print(f"Error: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 Starting API tests...\n")
    
    # Test streaming
    streaming_success = await test_jira_api_streaming()
    
    # Test non-streaming
    non_streaming_success = await test_jira_api_non_streaming()
    
    print(f"\n📋 Test Results:")
    print(f"   Streaming: {'✅ PASSED' if streaming_success else '❌ FAILED'}")
    print(f"   Non-streaming: {'✅ PASSED' if non_streaming_success else '❌ FAILED'}")
    
    overall_success = streaming_success and non_streaming_success
    print(f"\n🎯 Overall: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    return overall_success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
