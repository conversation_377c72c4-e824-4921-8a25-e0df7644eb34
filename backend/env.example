# 应用配置
APP_NAME=AI Toolbox Backend
APP_VERSION=0.1.0
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-secret-key-here-at-least-32-characters

# API配置
API_V1_PREFIX=/api/v1
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ORIGINS=["chrome-extension://*", "http://localhost:3000"]

# Azure AI配置
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your-azure-openai-api-key-if-using-key-auth
AZURE_OPENAI_API_VERSION=2023-05-15
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4o

# OpenAI配置（备用）
OPENAI_API_KEY=your-openai-api-key

# 数据库配置
DATABASE_URL=postgresql+asyncpg://localhost/ai_toolbox
DATABASE_ECHO=false

# Redis配置
REDIS_URL=redis://localhost:6379/0

# JWT配置
JWT_SECRET_KEY=your-jwt-secret-key-here-at-least-32-characters
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# 功能开关
ENABLE_CACHING=true
ENABLE_RATE_LIMITING=true
ENABLE_METRICS=true

# Microsoft OAuth Configuration (推荐使用，更安全)
MS_OAUTH_CLIENT_ID=your-azure-app-client-id
MS_OAUTH_CLIENT_SECRET=your-azure-app-client-secret
MS_OAUTH_GRANT_TYPE=client_credentials
MS_OAUTH_SCOPE=https://cognitiveservices.azure.com/.default
MS_OAUTH_URL=https://login.microsoftonline.com/your-tenant-id/oauth2/v2.0/token 