#!/usr/bin/env python3
"""Debug Azure OpenAI connection issues."""

import asyncio
import httpx
from app.config import settings
from app.services.azure_oauth import oauth_service

async def debug_azure_connection():
    """Debug Azure OpenAI connection step by step."""
    print("=== Azure OpenAI Connection Debug ===")
    
    # Step 1: Check settings
    print(f"1. Settings:")
    print(f"   Endpoint: {settings.azure_openai_endpoint}")
    print(f"   API Version: {settings.azure_openai_api_version}")
    print(f"   Deployment: {settings.azure_openai_deployment_name}")
    print(f"   Use OAuth: {settings.use_oauth_auth}")
    
    # Step 2: Test OAuth token
    try:
        print(f"\n2. Testing OAuth token...")
        token = await oauth_service.get_token()
        print(f"   ✅ Token obtained: {token[:20]}...")
        print(f"   Token length: {len(token)}")
    except Exception as e:
        print(f"   ❌ OAuth failed: {e}")
        return False
    
    # Step 3: Test direct HTTP connection to Azure
    try:
        print(f"\n3. Testing direct HTTP connection...")
        
        # Test basic connectivity to Azure endpoint
        async with httpx.AsyncClient(timeout=30.0) as client:
            # Just test if we can reach the endpoint
            test_url = f"{settings.azure_openai_endpoint.rstrip('/')}/openai/deployments/{settings.azure_openai_deployment_name}/chat/completions?api-version={settings.azure_openai_api_version}"
            
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10,
                "temperature": 0.1
            }
            
            print(f"   URL: {test_url}")
            print(f"   Headers: {headers}")
            
            response = await client.post(test_url, json=payload, headers=headers)
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            
            if response.status_code == 200:
                print("   ✅ Direct HTTP connection successful!")
                return True
            else:
                print(f"   ❌ HTTP connection failed: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"   ❌ Connection test failed: {e}")
        return False

async def test_azure_openai_client():
    """Test Azure OpenAI client directly."""
    print("\n=== Testing Azure OpenAI Client ===")
    
    try:
        from openai import AsyncAzureOpenAI
        
        token = await oauth_service.get_token()
        
        client = AsyncAzureOpenAI(
            azure_endpoint=settings.azure_openai_endpoint,
            azure_ad_token=token,
            api_version=settings.azure_openai_api_version,
        )
        
        print("Client created successfully")
        
        response = await client.chat.completions.create(
            model=settings.azure_openai_deployment_name,
            messages=[{"role": "user", "content": "Say hello"}],
            max_tokens=10,
            temperature=0.1
        )
        
        print(f"✅ Client test successful: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ Client test failed: {e}")
        return False

async def main():
    """Run all debug tests."""
    print("🔍 Starting connection debug...\n")
    
    # Test connection
    connection_ok = await debug_azure_connection()
    
    if connection_ok:
        # Test client
        client_ok = await test_azure_openai_client()
        
        if client_ok:
            print("\n🎯 All tests passed! Connection is working.")
        else:
            print("\n⚠️ Connection works but client has issues.")
    else:
        print("\n❌ Connection failed. Check network and credentials.")

if __name__ == "__main__":
    asyncio.run(main())
