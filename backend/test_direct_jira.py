#!/usr/bin/env python3
"""Test Jira generation directly without API layer."""

import asyncio
import json
from app.services.azure_ai import azure_ai_service

async def test_direct_jira():
    """Test Jira generation directly."""
    print("=== Direct Jira Generation Test ===")
    
    try:
        print("1. Testing non-streaming Jira generation...")
        result = await azure_ai_service.generate_jira_comment(
            task_description="实现用户注册功能，包括邮箱验证",
            task_type="feature",
            context={
                "priority": "high",
                "team": "frontend",
                "estimated_hours": 16
            }
        )
        
        print(f"✅ Non-streaming result:")
        print(f"   Generated content length: {len(result.get('generated_content', ''))}")
        print(f"   Model: {result.get('model', 'unknown')}")
        print(f"   Tokens: {result.get('tokens_used', 0)}")
        print(f"   Error: {result.get('error', 'None')}")
        
        if result.get('error'):
            print(f"❌ Non-streaming failed: {result['error']}")
            return False
        
        print("\n2. Testing streaming Jira generation...")
        full_content = ""
        chunk_count = 0
        
        async for chunk in azure_ai_service.generate_jira_comment_stream(
            task_description="修复登录页面的响应式布局问题",
            task_type="bug",
            context={
                "severity": "medium",
                "browser": "Chrome, Firefox"
            }
        ):
            if chunk.startswith("Error:"):
                print(f"❌ Streaming error: {chunk}")
                return False
            
            chunk_count += 1
            full_content += chunk
            if chunk_count <= 3:  # Show first few chunks
                print(f"   Chunk {chunk_count}: {chunk[:50]}...")
        
        print(f"✅ Streaming completed:")
        print(f"   Total chunks: {chunk_count}")
        print(f"   Total content length: {len(full_content)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_direct_jira())
    print(f"\nDirect Jira test: {'PASSED' if success else 'FAILED'}")
    exit(0 if success else 1)
