#!/usr/bin/env python3
"""Test only azure_ai_service without any API layer."""

import asyncio
import sys

async def test_azure_ai_only():
    """Test azure_ai_service directly."""
    print("=== Testing Azure AI Service Only ===")
    
    try:
        # Import here to avoid any startup issues
        from app.services.azure_ai import azure_ai_service
        
        print("1. Testing generate_jira_comment...")
        result = await azure_ai_service.generate_jira_comment(
            task_description="简单测试任务",
            task_type="test",
            context={"test": True}
        )
        
        print(f"✅ Result keys: {list(result.keys())}")
        print(f"✅ Generated content length: {len(result.get('generated_content', ''))}")
        print(f"✅ Error: {result.get('error', 'None')}")
        
        if result.get('error'):
            print(f"❌ Error occurred: {result['error']}")
            return False
        
        print("✅ Azure AI service test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_azure_ai_only())
    print(f"\nAzure AI test: {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
