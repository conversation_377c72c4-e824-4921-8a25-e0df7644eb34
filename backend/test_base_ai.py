#!/usr/bin/env python3
"""Test base_ai_service directly."""

import asyncio
import sys

async def test_base_ai():
    """Test base_ai_service directly."""
    print("=== Testing Base AI Service ===")
    
    try:
        from app.services.base_ai import base_ai_service
        
        print("1. Testing generate_text...")
        result = await base_ai_service.generate_text(
            prompt="简单测试：说hello",
            system_message="你是一个助手",
            max_tokens=50,
            temperature=0.1
        )
        
        print(f"✅ Result keys: {list(result.keys())}")
        print(f"✅ Success: {result.get('success', False)}")
        print(f"✅ Text length: {len(result.get('text', ''))}")
        print(f"✅ Error: {result.get('error', 'None')}")
        
        if result.get('error'):
            print(f"❌ Error occurred: {result['error']}")
            return False
        
        print("✅ Base AI service test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_base_ai())
    print(f"\nBase AI test: {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
