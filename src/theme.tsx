import {createTheme, <PERSON><PERSON>Provider} from "@mantine/core"
import {getPlasmoShadowContainer} from "../lib/utils";

const theme = createTheme({})
export const ThemeProvider = ({children}) => {
    return (
        <MantineProvider
            //https://github.com/PlasmoHQ/plasmo/issues/776#issuecomment-**********
            cssVariablesSelector=":host"
            theme={theme}>
            {children}
        </MantineProvider>
    )
}
